#!/usr/bin/env python3
"""
Gunicorn + Uvicorn 生产环境配置文件
高性能异步部署方案
"""

import multiprocessing
from pathlib import Path

# 项目根目录
PROJECT_ROOT = Path(__file__).parent
LOG_DIR = PROJECT_ROOT / "logs"

# 确保日志目录存在
LOG_DIR.mkdir(parents=True, exist_ok=True)

# 服务器配置
bind = "0.0.0.0:8000"
backlog = 2048

# 工作进程配置
workers = multiprocessing.cpu_count() * 2 + 1  # 根据CPU核心数自动计算
worker_class = "uvicorn.workers.UvicornWorker"  # 使用Uvicorn worker支持异步
worker_connections = 1000
max_requests = 1000  # 每个worker处理1000个请求后重启，防止内存泄漏
max_requests_jitter = 50  # 随机抖动，避免所有worker同时重启
timeout = 30  # 工作进程超时时间
keepalive = 2  # Keep-Alive连接等待时间

# 进程管理
preload_app = True  # 预加载应用，提高性能和内存使用效率
daemon = False  # 不以守护进程运行（由supervisor管理）
pidfile = str(LOG_DIR / "gunicorn.pid")

# 日志配置
accesslog = str(LOG_DIR / "gunicorn_access.log")
errorlog = str(LOG_DIR / "gunicorn_error.log")
loglevel = "info"
access_log_format = '%(h)s %(l)s %(u)s %(t)s "%(r)s" %(s)s %(b)s "%(f)s" "%(a)s" %(D)s'

# 环境变量
raw_env = [
    'PYTHONUNBUFFERED=1',
    'PYTHONDONTWRITEBYTECODE=1',
]


# 钩子函数
def on_starting(server):
    """服务器启动时的钩子"""
    server.log.info("Gunicorn + Uvicorn 服务器正在启动...")
    server.log.info(f"工作进程数: {workers}")
    server.log.info(f"Worker类型: {worker_class}")

def when_ready(server):
    """服务器准备就绪时的钩子"""
    server.log.info("Gunicorn + Uvicorn 服务器已准备就绪")
    server.log.info(f"监听地址: {bind}")

def post_worker_init(worker):
    """工作进程初始化后的钩子"""
    # 设置worker环境变量，用于识别多进程环境
    os.environ['GUNICORN_WORKER'] = 'true'
    os.environ['WORKER_PID'] = str(worker.pid)
    worker.log.info(f"Uvicorn Worker {worker.pid} 初始化完成")

def worker_exit(server, worker):
    """工作进程退出时的钩子"""
    server.log.info(f"Uvicorn Worker {worker.pid} 正在退出")

def on_exit(server):
    """服务器退出时的钩子"""
    server.log.info("Gunicorn + Uvicorn 服务器正在关闭...")
